package com.illumio.data.util;

import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.metrics.LongCounter;
import io.opentelemetry.api.metrics.Meter;
import org.springframework.stereotype.Component;

@Component
public class MetricsUtil {
    private final LongCounter insightIPClassificationEventCounter;
    private final LongCounter insightIPClassificationWrongEvent;
    private final LongCounter ipClassificationOutgoingEvent;

    private LongCounter createCounter(Meter meter, String name, String description) {
        return meter.counterBuilder(name)
            .setDescription(description)
            .build();
    }

    public MetricsUtil(Meter meter) {

        // Use existing beans
        this.insightIPClassificationEventCounter = this.createCounter(meter, FlowDataValidator.INSIGHTS_INCOMING_EVENTS,
            "Used to count the number of IP Classification events");
        this.insightIPClassificationWrongEvent = this.createCounter(meter, FlowDataValidator.INSIGHTS_INCOMING_WRONG_EVENTS,
            "Used to count the number of malformed events that reached IP Classification");
        this.ipClassificationOutgoingEvent = this.createCounter(meter, FlowDataValidator.INSIGHTS_OUTGOING_EVENTS,
            "Used to count the number of IP Classification outgoing events");
    }

    public void incrementIPClassificationEvent(Attributes attributes) {
        insightIPClassificationEventCounter.add(1L, attributes);
    }

    public void incrementIPClassificationWrongEvent(Attributes attributes) {
        insightIPClassificationWrongEvent.add(1L, attributes);
    }

    public void incrementIPClassificationOutgoingEvent(Attributes attributes) {
        ipClassificationOutgoingEvent.add(1L, attributes);
    }
}
