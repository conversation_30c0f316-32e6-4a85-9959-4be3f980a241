# Default values for iqs.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# This will set the replicaset count more information can be found here: https://kubernetes.io/docs/concepts/workloads/controllers/replicaset/
replicaCount: 2

# This sets the container image more information can be found here: https://kubernetes.io/docs/concepts/containers/images/
image:
  repositoryBase: "illum.azurecr.io/"
  repositoryName: "insights-query-service"
  metadataDbRepositoryName: "utilities/db-create"
  metadataDbRepositoryTag: 1.0.0-beta
  pullPolicy: Always
  tag: # value given at helm deployment

# This is for the secretes for pulling an image from a private repository more information can be found here: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
imagePullSecrets: []
# This is to override the chart name.
nameOverride: ""
fullnameOverride: ""

# This section builds out the service account more information can be found here: https://kubernetes.io/docs/concepts/security/service-accounts/
serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

# This is for setting Kubernetes Annotations to a Pod.
# For more information checkout: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
podAnnotations: {}
# This is for setting Kubernetes Labels to a Pod.
# For more information checkout: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
podLabels: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

jwt:
  secret: _DO_NOT_COMMIT_ # picked from argo config, defined in vault

ports:
  - name: rest
    port: 8081

servicePorts:
  - name: rest
    podPort: rest
    servicePort: 8081

service:
  type: ClusterIP

ingress:
  tlsSecretName: "iqs-tls"
  annotations: {}
  ingressClassName: "nginx"
  enabled: false
  certManager:
    enabled: false
    clusterIssuer: "cert-manager-letsencrypt-prod-route53"
  fqdn: ""
  proxy_fqdn: ""

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

# This section is for setting up autoscaling more information can be found here: https://kubernetes.io/docs/concepts/workloads/autoscaling/
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

podMetricsAnnotations:
  prometheus.io/path: /metrics
  prometheus.io/port: "9464"
  prometheus.io/scheme: http
  prometheus.io/scrape: "true"

# Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}

logging:
  level:
    root: INFO

insightsConfig:
  kustoConfig:
    clusterUri: https://arch-kusto.eastus.kusto.windows.net
    database: DecoratedFlows
    isManagedIdentity: false
    azureClientId: _DO_NOT_COMMIT_ # picked from argo config, defined in vault
    azureClientSecret: _DO_NOT_COMMIT_ # picked from argo config, defined in vault
    azureTenantId: _DO_NOT_COMMIT_ # picked from argo config, defined in vault
  kustoInsightsConfig:
    clusterUri: https://arch-kusto.eastus.kusto.windows.net
    database: DecoratedFlows
    isManagedIdentity: false
    azureClientId: _DO_NOT_COMMIT_ # picked from argo config, defined in vault
    azureClientSecret: _DO_NOT_COMMIT_ # picked from argo config, defined in vault
    azureTenantId: _DO_NOT_COMMIT_ # picked from argo config, defined in vault
  riskyServiceConfig:
    riskyServiceFilePath: "classpath:risky_services.csv"
  llmServiceConfig:
    llmServiceFilePath: "classpath:llm_services.csv"
  unencryptedServiceConfig:
    unencryptedServiceFilePath: "classpath:dora_unencrypted_services.csv"
  jwtConfig:
    enableJwt: false
    isSymmetricKey: false
  apiConfig:
    authKey: _DO_NOT_COMMIT_ # picked from argo config, defined in vault
  pagination:
    maxPageSize: 100
    defaultPageSize: 25
    defaultPageNumber: 1
  kafkaProducerConfig:
    bootstrapServers: sunnyvale-enf-kafka-eventhub-ns.servicebus.windows.net:9093
    isConnectionString: true
    isManagedIdentity: false
    topic: cs-sync-inventory2pce-v1
    connectionString: _VAULT_CONNECTION_STRING_
  quarantineConfig:
    labelId: "18577348462903458"
  urlConfig:
    uiUrl: https://console.sunnyvale.ilabs.io
  liquibaseConfig:
    changelogPath:
  proxyConfig:
    enableProxy: false
    targetFqdn: https://other-cluster.example.com
    targetEndpoint: /api/v1/resource-insights
    timeoutSeconds: 30
eventhub:
  password: _DO_NOT_COMMIT_ # eventhub connection string
resourceConfig:
  enabled: false
  resourceMap:
    - resourceId: /subscriptions/d74212c4-cc55-406e-85b1-ea45fb89ea03/resourceGroups/rg_sauto2_1/providers/Microsoft.Network/networkInterfaces/vnet_sauto2-nic01-5b8734c6
      severityLevel: HIGH
      VEScore: 8.6
      sentBytes: 3100
      receivedBytes: 57708104
      flowCount: 58
    - resourceId: /subscriptions/87332a70-7c1b-4437-aa3b-ec7c00d72de0/resourceGroups/nebula-inventory-test-vm-1-rg/providers/Microsoft.Compute/virtualMachines/nebula-inventory-test-vm-1-myVM
      severityLevel: MEDIUM
      VEScore: 6.3
      sentBytes: 100112
      receivedBytes: 810410
      flowCount: 8990
    - resourceId: /subscriptions/87332a70-7c1b-4437-aa3b-ec7c00d72de0/resourceGroups/nebula-inventory-test-vm-1-rg/providers/Microsoft.Compute/virtualMachines/nebula-inventory-test-vm-1-myVM
      severityLevel: MEDIUM
      VEScore: 6.3
      sentBytes: 100112
      receivedBytes: 810410
      flowCount: 8990
    - resourceId: /subscriptions/87332a70-7c1b-4437-aa3b-ec7c00d72de0/resourceGroups/nebula-inventory-test-vm-1-rg/providers/Microsoft.Compute/virtualMachines/nebula-inventory-test-vm-1-myVM
      severityLevel: MEDIUM
      VEScore: 6.3
      sentBytes: 100112
      receivedBytes: 810410
      flowCount: 8990
    - resourceId: /subscriptions/87332a70-7c1b-4437-aa3b-ec7c00d72de0/resourceGroups/nebula-inventory-test-vm-1-rg/providers/Microsoft.Compute/virtualMachines/nebula-inventory-test-vm-1-myVM
      severityLevel: MEDIUM
      VEScore: 6.3
      sentBytes: 100112
      receivedBytes: 810410
      flowCount: 8990

jvmOptions: "-Xms4g -Xmx6g -XX:+CrashOnOutOfMemoryError"

liquibase:
  enabled:
  changelog:

metadataDbConfig:
  driverClassName:
  host: sunnyvale-cloudsecure-admin-1-postgres.postgres.database.azure.com
  port: 5432
  database: postgres
  username: postgres
  password: _DO_NOT_COMMIT_ # picked from argo config, defined in vault
  metadataDbName: metadata
  type: postgresql

serviceAuth:
  keys:
