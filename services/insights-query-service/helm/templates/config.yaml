apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "iqs.fullname" . }}-env-configmap
  labels:
    {{- include "iqs.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}
    jwt:
      secret: "{{.Values.jwt.secret}}"
    server:
      port:
        8081
    spring:
      application:
        name: insights-query-service
      output:
        ansi:
          enabled: ALWAYS
      main:
        web-application-type: reactive
      r2dbc:
        url: "r2dbc:{{ .Values.metadataDbConfig.type }}://{{ .Values.metadataDbConfig.host }}:{{ .Values.metadataDbConfig.port }}/{{ .Values.metadataDbConfig.metadataDbName }}?sslMode=REQUIRE"
        username: "{{.Values.metadataDbConfig.username}}"
        password: "{{.Values.metadataDbConfig.password}}"
      liquibase:
        enabled: "{{.Values.liquibase.enabled}}"
        change-log: "{{.Values.liquibase.changelog}}"
    
    insights-config:
      kustoConfig:
        clusterUri: "{{.Values.insightsConfig.kustoConfig.clusterUri}}"
        database: "{{.Values.insightsConfig.kustoConfig.database}}"
        isManagedIdentity: "{{.Values.insightsConfig.kustoConfig.isManagedIdentity}}"
        azureClientId: "{{.Values.insightsConfig.kustoConfig.azureClientId}}"
        azureClientSecret: "{{.Values.insightsConfig.kustoConfig.azureClientSecret}}"
        azureTenantId: "{{.Values.insightsConfig.kustoConfig.azureTenantId}}"
      kustoInsightsConfig:
        clusterUri: "{{.Values.insightsConfig.kustoInsightsConfig.clusterUri}}"
        database: "{{.Values.insightsConfig.kustoInsightsConfig.database}}"
        isManagedIdentity: "{{.Values.insightsConfig.kustoInsightsConfig.isManagedIdentity}}"
        azureClientId: "{{.Values.insightsConfig.kustoInsightsConfig.azureClientId}}"
        azureClientSecret: "{{.Values.insightsConfig.kustoInsightsConfig.azureClientSecret}}"
        azureTenantId: "{{.Values.insightsConfig.kustoInsightsConfig.azureTenantId}}"
      riskyServiceConfig:
        riskyServiceFilePath: "{{.Values.insightsConfig.riskyServiceConfig.riskyServiceFilePath}}"
      unencryptedServiceConfig:
        unencryptedServiceFilePath: "{{.Values.insightsConfig.unencryptedServiceConfig.unencryptedServiceFilePath}}"
      llmServiceConfig:
        llmServiceFilePath: "{{.Values.insightsConfig.llmServiceConfig.llmServiceFilePath}}"
      jwtConfig:
        enableJwt: "{{.Values.insightsConfig.jwtConfig.enableJwt}}"
        isSymmetricKey: "{{.Values.insightsConfig.jwtConfig.isSymmetricKey}}"
      apiConfig:
        authKey: "{{.Values.insightsConfig.apiConfig.authKey}}"
      paginationConfig:
        maxPageSize: "{{.Values.insightsConfig.pagination.maxPageSize}}"
        defaultPageSize: "{{.Values.insightsConfig.pagination.defaultPageSize}}"
        defaultPageNumber: "{{.Values.insightsConfig.pagination.defaultPageNumber}}"
      kafkaProducerConfig:
        bootstrapServers: "{{.Values.insightsConfig.kafkaProducerConfig.bootstrapServers}}"
        isConnectionString: "{{.Values.insightsConfig.kafkaProducerConfig.isConnectionString}}"
        isManagedIdentity: "{{.Values.insightsConfig.kafkaProducerConfig.isManagedIdentity}}"
        topic: "{{.Values.insightsConfig.kafkaProducerConfig.topic}}"
        saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{.Values.insightsConfig.kafkaProducerConfig.connectionString}}";
      quarantineConfig:
        labelId: "{{.Values.insightsConfig.quarantineConfig.labelId}}"
      urlConfig:
        uiUrl: "{{.Values.insightsConfig.urlConfig.uiUrl}}"
      metadataJdbcConfig:
        driverClassName: "{{.Values.metadataDbConfig.driverClassName}}"
        host: "{{.Values.metadataDbConfig.host}}"
        port: "{{.Values.metadataDbConfig.port}}"
        database: "{{.Values.metadataDbConfig.metadataDbName}}"
        username: "{{.Values.metadataDbConfig.username}}"
        password: "{{.Values.metadataDbConfig.password}}"
      liquibaseConfig:
        changelogPath: "{{.Values.insightsConfig.liquibaseConfig.changelogPath}}"
      proxyConfig:
        enableProxy: "{{.Values.insightsConfig.proxyConfig.enableProxy}}"
        targetFqdn: "{{.Values.insightsConfig.proxyConfig.targetFqdn}}"
        targetEndpoint: "{{.Values.insightsConfig.proxyConfig.targetEndpoint}}"
        timeoutSeconds: "{{.Values.insightsConfig.proxyConfig.timeoutSeconds}}"
    resource-config:
      enabled: {{ .Values.resourceConfig.enabled | quote }}
      resourceMap:
      {{- range .Values.resourceConfig.resourceMap }}
        - resourceId: {{ .resourceId | quote }}
          severityLevel: {{ .severityLevel | quote }}
          VEScore: {{ .VEScore }}
          sentBytes: {{ .sentBytes }}
          receivedBytes: {{ .receivedBytes }}
          flowCount: {{ .flowCount }}
      {{- end }}
    service-auth:
      keys: {{ toYaml .Values.serviceAuth.keys | nindent 8 }}
        
