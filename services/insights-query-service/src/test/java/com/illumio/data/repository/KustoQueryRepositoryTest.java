package com.illumio.data.repository;

import com.illumio.data.model.DerivedMetadata;
import com.illumio.data.model.Filters;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Fields;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.constants.TableType;
import com.illumio.data.model.constants.WidgetId;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.AGGREGATED_BYTES;
import static com.illumio.data.model.constants.Fields.AGGREGATED_FLOWS;
import static com.illumio.data.model.constants.Fields.DESTINATION_IP;
import static com.illumio.data.model.constants.Fields.DESTINATION_RESOURCE_CATEGORY;
import static com.illumio.data.model.constants.Fields.DESTINATION_RESOURCE_ID;
import static com.illumio.data.model.constants.Fields.DESTINATION_RESOURCE_NAME;
import static com.illumio.data.model.constants.Fields.DESTINATION_RESOURCE_TYPE;
import static com.illumio.data.model.constants.Fields.DESTINATION_ROLE;
import static com.illumio.data.model.constants.Fields.DESTINATION_ZONE;
import static com.illumio.data.model.constants.Fields.PORT;
import static com.illumio.data.model.constants.Fields.PROTOCOL;
import static com.illumio.data.model.constants.Fields.SOURCE_COUNTRY;
import static com.illumio.data.model.constants.Fields.SOURCE_IP;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_CATEGORY;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_ID;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_NAME;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_TYPE;
import static com.illumio.data.model.constants.Fields.SOURCE_ROLE;
import static com.illumio.data.model.constants.Fields.SOURCE_ZONE;
import static org.junit.jupiter.api.Assertions.assertIterableEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.fail;

class KustoQueryRepositoryTest {

    private KustoQueryRepository repository;
    private RequestPayload payload;
    private final String tenantId = "testTenant";
    private RequestContext requestContext;


    @BeforeEach
    void setUp() {
        repository = new KustoQueryRepository();
    }

    @Test
    void testGetQueryString_RiskyServiceTraffic() {
        generateRequestContext();
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.RISKY_SERVICE_TRAFFIC)
                .groupByFields(List.of(PORT, PROTOCOL))
                .summarizeFields(List.of(AGGREGATED_FLOWS, AGGREGATED_BYTES))
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);
        assertTrue(query.contains("Insights_RiskyTraffic_Hourly"), "Query should contain the base table");
        assertTrue(query.contains(String.format("| where IllumioTenantId == '%s'", tenantId)), "Query should contain tenant condition");
        assertTrue(query.contains("| summarize"), "Query should include grouping logic");
    }

    @Test
    void testGetQueryString_ZoneLevelTraffic() {
        generateRequestContext();
        Metadata metadata = Metadata.builder()
                .groupByFields(List.of(SOURCE_ZONE, DESTINATION_ZONE))
                .summarizeFields(List.of(AGGREGATED_FLOWS, AGGREGATED_BYTES))
                .widgetId(WidgetId.ZONE_LEVEL_TRAFFIC)
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);
        assertTrue(query.contains("Insights_RiskyTraffic_Hourly"), "Query should contain the base table");
        assertTrue(query.contains(String.format("| where IllumioTenantId == '%s'", tenantId)), "Query should contain tenant condition");
        assertTrue(query.contains("| summarize"), "Query should include grouping logic");
        assertTrue(query.contains("let totalRows = toscalar(rows | count);"), "Query should include pagination logic");
    }

    @Test
    void testGetQueryString_DestRoleTraffic() {
        generateRequestContext();
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.DESTINATION_ROLE_LEVEL_TRAFFIC)
                .groupByFields(List.of(SOURCE_RESOURCE_ID, SOURCE_RESOURCE_NAME, SOURCE_RESOURCE_CATEGORY, SOURCE_RESOURCE_TYPE, DESTINATION_ROLE))
                .summarizeFields(List.of(AGGREGATED_FLOWS, AGGREGATED_BYTES))
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);
        assertTrue(query.contains("Insights_RiskyTraffic_Hourly"), "Query should contain the base table");
        assertTrue(query.contains(String.format("| where IllumioTenantId == '%s'", tenantId)), "Query should contain tenant condition");
        assertTrue(query.contains("| summarize"), "Query should include grouping logic");
        assertTrue(query.contains("|sort by"), "Query should include sorting logic");
        assertTrue(query.contains("let totalRows = toscalar(rows | count);"), "Query should include pagination logic");
    }

    @Test
    void testGetQueryString_TopDestinationRoles() {
        generateRequestContext();
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.TOP_DESTINATION_ROLES)
                .groupByFields(List.of(DESTINATION_ROLE))
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);

        assertTrue(query.contains("let byFlows = "), "Query should start with let byFlows");
        assertTrue(query.contains("let byBytes = "), "Query should contain let byBytes");
        assertTrue(query.contains("let byFlowsPrev = "), "Query should have previous flows");
        assertTrue(query.contains("let byBytesPrev = "), "Query should have previous bytes");
        assertTrue(query.contains("union finalFlows, finalBytes;"), "Query should union the two subqueries");
    }

    @Test
    void testGetQueryString_TopWorkloads() {
        generateRequestContext();
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.TOP_WORKLOADS)
                .groupByFields(List.of(SOURCE_RESOURCE_ID, SOURCE_RESOURCE_NAME, SOURCE_RESOURCE_CATEGORY, SOURCE_RESOURCE_TYPE))
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);

        assertTrue(query.contains("let byFlows = "), "Query should contain byFlows");
        assertTrue(query.contains("let byBytes = "), "Query should contain byBytes");
        assertTrue(query.contains("let topWByFlows = "), "Query should contain topWByFlows");
        assertTrue(query.contains("let topWByBytes = "), "Query should contain topWByBytes");
        assertTrue(query.contains("union topWByFlows, topWByBytes"), "Query should union the top workloads queries");
    }

    @Test
    void testGetQueryString_ThreatMap() {
        generateRequestContext();
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.THREAT_MAP)
                .groupByFields(List.of(SOURCE_COUNTRY))
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);

        assertTrue(query.contains("let topByflows = "), "Query should contain byFlows");
        assertTrue(query.contains("let topBybytes = "), "Query should contain byBytes");
        assertTrue(query.contains("let prevByflows = "), "Query should contain topWByFlows");
        assertTrue(query.contains("let prevBybytes = "), "Query should contain topWByBytes");
        assertTrue(query.contains("union flows, bytes"), "Query should union the top workloads queries");
    }

    @Test
    void testGetQueryString_TopRoles() {
        generateRequestContext();
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.TOP_ROLES)
                .groupByFields(List.of(DESTINATION_ROLE))
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);

        assertTrue(query.contains("let topByflows = "), "Query should contain byFlows");
        assertTrue(query.contains("let topBybytes = "), "Query should contain byBytes");
        assertTrue(query.contains("let prevByflows = "), "Query should contain topWByFlows");
        assertTrue(query.contains("let prevBybytes = "), "Query should contain topWByBytes");
        assertTrue(query.contains("union flows, bytes"), "Query should union the top workloads queries");
    }

    @Test
    void testGetQueryString_TopServices() {
        generateRequestContext();
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.TOP_SERVICES)
                .groupByFields(List.of(PORT, PROTOCOL))
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);

        assertTrue(query.contains("let topByflows = "), "Query should contain byFlows");
        assertTrue(query.contains("let topBybytes = "), "Query should contain byBytes");
        assertTrue(query.contains("let prevByflows = "), "Query should contain topWByFlows");
        assertTrue(query.contains("let prevBybytes = "), "Query should contain topWByBytes");
        assertTrue(query.contains("union flows, bytes"), "Query should union the top workloads queries");
    }

    @Test
    void testGetQueryString_TopIP() {
        generateRequestContext();
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.TOP_MALICIOUS_IPS)
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);

        assertTrue(query.contains("let topIpByflows = "), "Query should contain byFlows");
        assertTrue(query.contains("let topIpBybytes = "), "Query should contain byBytes");
        assertTrue(query.contains("let prevIpByflows = "), "Query should contain topWByFlows");
        assertTrue(query.contains("let prevIpBybytes = "), "Query should contain topWByBytes");
        assertTrue(query.contains("union flows, bytes"), "Query should union the top workloads queries");
    }

    // this test will check filters for direction and trafficstatus
    @Test
    void testGetQueryString_TopIPWithFilters() {
        generateRequestContext();
        RequestPayload payload = requestContext.getRequestPayload().orElseThrow();
        payload.setFilters(List.of(
                Filters.builder()
                        .categoryName("traffic_direction")
                        .categoryValue(List.of("INBOUND"))
                        .categoryType("string")
                        .build(),
                Filters.builder()
                        .categoryName("traffic_status")
                        .categoryValue(List.of("ALLOWED"))
                        .categoryType("string")
                        .build()
        ));
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.TOP_MALICIOUS_IPS)
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);
        assertTrue(query.contains(" | where TrafficStatus in ('ALLOWED')"), "Query should contain traffic status filter");
    }

    // with no filter of trafficstatus
    @Test
    void testGetQueryString_TopIPWithNoFilters() {
        generateRequestContext();
        RequestPayload payload = requestContext.getRequestPayload().orElseThrow();
        payload.setFilters(List.of(
                Filters.builder()
                        .categoryName("traffic_direction")
                        .categoryValue(List.of("INBOUND"))
                        .categoryType("string")
                        .build()
        ));
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.TOP_MALICIOUS_IPS)
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);
        assertFalse(query.contains(" | where TrafficStatus in ('ALLOWED')"), "Query should not contain traffic status filter");
    }

    @Test
    void testGetQueryString_TopCategory() {
        generateRequestContext();
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.TOP_CATEGORY_WITH_MALICIOUS_IP)
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);

        assertTrue(query.contains("let topByflows = "), "Query should contain byFlows");
        assertTrue(query.contains("let topBybytes = "), "Query should contain byBytes");
        assertTrue(query.contains("let prevIpByflows = "), "Query should contain topWByFlows");
        assertTrue(query.contains("let prevIpBybytes = "), "Query should contain topWByBytes");
        assertTrue(query.contains("union flows, bytes"), "Query should union the top workloads queries");

    }

    @Test
    void testGetQueryString_DoraUnencryptedServices() {
        generateRequestContext();
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.UNENCRYPTED_SERVICES)
                .summarizeFields(List.of(AGGREGATED_FLOWS, AGGREGATED_BYTES))
                .groupByFields(List.of(PORT, PROTOCOL))
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);

        assertNotNull(query);
        assertTrue(query.contains("Insights_RiskyTraffic_Hourly"), "Query should contain the base table");
        assertTrue(query.contains(String.format("| where IllumioTenantId == '%s'", tenantId)), "Query should contain tenant condition");
        assertTrue(query.contains("by Port, Proto"), "Query should include grouping logic");
        assertTrue(query.contains("| where IsUnencrypted =="), "Query should include filter logic");
        assertTrue(query.contains("|sort by"), "Query should include sorting logic");
    }

    @Test
    void testGetQueryString_TopIct() {
        generateRequestContext();
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.DORA_TOP_ICT)
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);
        assertTrue(query.contains(String.format("| where IllumioTenantId == '%s'", tenantId)), "Query should contain tenant condition");
        assertTrue(query.contains("let inboundCurrFlows = curr"), "Query should include inbound current flows");
        assertTrue(query.contains("let outboundCurrFlows = curr"), "Query should include outbound current flows");
        assertTrue(query.contains("let outboundPrevBytes = prev"), "Query should include outbound previous bytes");
        assertTrue(query.contains("let outboundPrevFlows = prev"), "Query should include outbound previous flows");
        assertTrue(query.contains("union inboundFlows,outboundFlows"), "Query should include union between inbound and outbound");
        assertTrue(query.contains("union flows,bytes"), "Query should include final union logic");
    }

    @Test
    void testGetQueryString_CriticalIct() {
        generateRequestContext();
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.DORA_CRITICAL_ICT)
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);
        assertTrue(query.contains(String.format("| where IllumioTenantId == '%s'", tenantId)), "Query should contain tenant condition");
        assertTrue(query.contains("let riskyDestRolesCurrFlows = curr"), "Query should include expected tables");
        assertTrue(query.contains("let riskySrcRolesCurrFlows = curr"), "Query should include expected tables");
        assertTrue(query.contains("let riskyDestRolesPrevFlows = prev"), "Query should include expected tables");
        assertTrue(query.contains("let riskySrcRolesPrevBytes = prev"), "Query should include expected tables");
        assertTrue(query.contains("$left.SourceLabel == $right.DestinationLabel"), "Query should include join logic");
        assertTrue(query.contains("union flows, bytes"), "Query should include final union logic");
    }

    @SneakyThrows
    @Test
    void testGetIpDataTransferInbound() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.IP_DATA_TRANSFER_INBOUND)
                .tableName("InfiltrationTraffic")
                .summarizeFields(List.of(AGGREGATED_FLOWS, AGGREGATED_BYTES))
                .groupByFields(List.of(SOURCE_IP))
                .build();

        TimeFrame currentTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();

        RequestPayload requestPayload = RequestPayload.builder().build();
        requestPayload.setCurrentTimeFrame(currentTimeFrame);
        requestPayload.setComparisonTimeFrame(comparisonTimeFrame);
        requestPayload.setFilters(List.of(Filters.builder()
                        .categoryName("traffic_direction")
                        .categoryValue(List.of("INBOUND"))
                        .categoryType("string")
                        .build(),
                Filters.builder()
                        .categoryName("ip")
                        .categoryValue(List.of("********"))
                        .categoryType("string")
                        .build()));
        requestPayload.setSortByFields(Collections.emptyList());
        DerivedMetadata derivedMetadata = new DerivedMetadata(
                "Insights_InfiltrationTraffic_Daily",
                "Insights_InfiltrationTraffic_Daily",
                currentTimeFrame,
                comparisonTimeFrame,
                TableType.AGGREGATED_TABLE,
                TableType.AGGREGATED_TABLE
        );

        RequestContext request = RequestContext.builder()
                .tenantId(Optional.of("tenant_test"))
                .requestPayload(Optional.of(requestPayload))
                .derivedMetadata(Optional.of(derivedMetadata))
                .build();

        String query = repository.getQueryString(request, metadata);

        assertIterableEquals(
                normalizeQueryParts(readFileFromClassPath("unit-test-examples/IpDataTransferInbound_IpFilter.txt")),
                normalizeQueryParts(query)
        );
    }

    @SneakyThrows
    @Test
    void testGetIpDataTransferOutbound() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.IP_DATA_TRANSFER_OUTBOUND)
                .tableName("ExfiltrationTraffic")
                .summarizeFields(List.of(AGGREGATED_FLOWS, AGGREGATED_BYTES))
                .groupByFields(List.of(DESTINATION_IP))
                .build();

        TimeFrame currentTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();

        RequestPayload requestPayload = RequestPayload.builder().build();
        requestPayload.setCurrentTimeFrame(currentTimeFrame);
        requestPayload.setComparisonTimeFrame(comparisonTimeFrame);
        requestPayload.setFilters(List.of(Filters.builder()
                        .categoryName("traffic_direction")
                        .categoryValue(List.of("OUTBOUND"))
                        .categoryType("string")
                        .build(),
                Filters.builder()
                        .categoryName("ip")
                        .categoryValue(List.of("********"))
                        .categoryType("string")
                        .build()));
        requestPayload.setSortByFields(Collections.emptyList());
        DerivedMetadata derivedMetadata = new DerivedMetadata(
                "Insights_ExfiltrationTraffic_Daily",
                "Insights_ExfiltrationTraffic_Daily",
                currentTimeFrame,
                comparisonTimeFrame,
                TableType.AGGREGATED_TABLE,
                TableType.AGGREGATED_TABLE
        );

        RequestContext request = RequestContext.builder()
                .tenantId(Optional.of("tenant_test"))
                .requestPayload(Optional.of(requestPayload))
                .derivedMetadata(Optional.of(derivedMetadata))
                .build();

        String query = repository.getQueryString(request, metadata);

        assertIterableEquals(
                normalizeQueryParts(readFileFromClassPath("unit-test-examples/IpDataTransferOutbound_IpFilter.txt")),
                normalizeQueryParts(query)
        );
    }

    @SneakyThrows
    @Test
    void testGetTopSourceResources() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.IP_TOP_SOURCE_RESOURCES)
                .tableName("ExfiltrationTraffic")
                .summarizeFields(List.of(AGGREGATED_FLOWS, AGGREGATED_BYTES))
                .groupByFields(List.of(
                        SOURCE_RESOURCE_ID,
                        SOURCE_RESOURCE_NAME,
                        SOURCE_RESOURCE_CATEGORY,
                        SOURCE_RESOURCE_TYPE
                ))
                .build();

        TimeFrame currentTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();

        RequestPayload requestPayload = RequestPayload.builder().build();
        requestPayload.setCurrentTimeFrame(currentTimeFrame);
        requestPayload.setComparisonTimeFrame(comparisonTimeFrame);
        requestPayload.setFilters(List.of(Filters.builder()
                        .categoryName("traffic_direction")
                        .categoryValue(List.of("OUTBOUND"))
                        .categoryType("string")
                        .build(),
                Filters.builder()
                        .categoryName("ip")
                        .categoryValue(List.of("********"))
                        .categoryType("string")
                        .build()));
        requestPayload.setSortByFields(Collections.emptyList());
        DerivedMetadata derivedMetadata = new DerivedMetadata(
                "Insights_ExfiltrationTraffic_Daily",
                "Insights_ExfiltrationTraffic_Daily",
                currentTimeFrame,
                comparisonTimeFrame,
                TableType.AGGREGATED_TABLE,
                TableType.AGGREGATED_TABLE
        );

        RequestContext request = RequestContext.builder()
                .tenantId(Optional.of("tenant_test"))
                .requestPayload(Optional.of(requestPayload))
                .derivedMetadata(Optional.of(derivedMetadata))
                .build();

        String query = repository.getQueryString(request, metadata);

        assertIterableEquals(
                normalizeQueryParts(readFileFromClassPath("unit-test-examples/IpTopSourceResources_IpFilter.txt")),
                normalizeQueryParts(query)
        );
    }

    @SneakyThrows
    @Test
    void testGetTopDestinationResources() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.IP_TOP_DESTINATION_RESOURCES)
                .tableName("InfiltrationTraffic")
                .summarizeFields(List.of(AGGREGATED_FLOWS, AGGREGATED_BYTES))
                .groupByFields(List.of(
                        DESTINATION_RESOURCE_ID,
                        DESTINATION_RESOURCE_NAME,
                        DESTINATION_RESOURCE_CATEGORY,
                        DESTINATION_RESOURCE_TYPE
                ))
                .build();

        TimeFrame currentTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();

        RequestPayload requestPayload = RequestPayload.builder().build();
        requestPayload.setCurrentTimeFrame(currentTimeFrame);
        requestPayload.setComparisonTimeFrame(comparisonTimeFrame);
        requestPayload.setFilters(List.of(Filters.builder()
                        .categoryName("traffic_direction")
                        .categoryValue(List.of("INBOUND"))
                        .categoryType("string")
                        .build(),
                Filters.builder()
                        .categoryName("ip")
                        .categoryValue(List.of("********"))
                        .categoryType("string")
                        .build()));
        requestPayload.setSortByFields(Collections.emptyList());
        DerivedMetadata derivedMetadata = new DerivedMetadata(
                "Insights_InfiltrationTraffic_Daily",
                "Insights_InfiltrationTraffic_Daily",
                currentTimeFrame,
                comparisonTimeFrame,
                TableType.AGGREGATED_TABLE,
                TableType.AGGREGATED_TABLE
        );

        RequestContext request = RequestContext.builder()
                .tenantId(Optional.of("tenant_test"))
                .requestPayload(Optional.of(requestPayload))
                .derivedMetadata(Optional.of(derivedMetadata))
                .build();

        String query = repository.getQueryString(request, metadata);

        assertIterableEquals(
                normalizeQueryParts(readFileFromClassPath("unit-test-examples/IpTopDestinationResources_IpFilter.txt")),
                normalizeQueryParts(query)
        );
    }

    @SneakyThrows
    @Test
    void testGetTopSourceRoles() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.IP_TOP_SOURCE_ROLES)
                .tableName("ExfiltrationTraffic")
                .summarizeFields(List.of(AGGREGATED_FLOWS, AGGREGATED_BYTES))
                .groupByFields(List.of(SOURCE_ROLE))
                .build();

        TimeFrame currentTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();

        RequestPayload requestPayload = RequestPayload.builder().build();
        requestPayload.setCurrentTimeFrame(currentTimeFrame);
        requestPayload.setComparisonTimeFrame(comparisonTimeFrame);
        requestPayload.setFilters(List.of(Filters.builder()
                        .categoryName("traffic_direction")
                        .categoryValue(List.of("OUTBOUND"))
                        .categoryType("string")
                        .build(),
                Filters.builder()
                        .categoryName("ip")
                        .categoryValue(List.of("********"))
                        .categoryType("string")
                        .build()));
        requestPayload.setSortByFields(Collections.emptyList());
        DerivedMetadata derivedMetadata = new DerivedMetadata(
                "Insights_ExfiltrationTraffic_Daily",
                "Insights_ExfiltrationTraffic_Daily",
                currentTimeFrame,
                comparisonTimeFrame,
                TableType.AGGREGATED_TABLE,
                TableType.AGGREGATED_TABLE
        );

        RequestContext request = RequestContext.builder()
                .tenantId(Optional.of("tenant_test"))
                .requestPayload(Optional.of(requestPayload))
                .derivedMetadata(Optional.of(derivedMetadata))
                .build();

        String query = repository.getQueryString(request, metadata);

        assertIterableEquals(
                normalizeQueryParts(readFileFromClassPath("unit-test-examples/IpTopSourceRoles_IpFilter.txt")),
                normalizeQueryParts(query)
        );
    }

    @SneakyThrows
    @Test
    void testGetTopDestinationRoles() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.IP_TOP_DESTINATION_ROLES)
                .tableName("InfiltrationTraffic")
                .summarizeFields(List.of(AGGREGATED_FLOWS, AGGREGATED_BYTES))
                .groupByFields(List.of(DESTINATION_ROLE))
                .build();

        TimeFrame currentTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();

        RequestPayload requestPayload = RequestPayload.builder().build();
        requestPayload.setCurrentTimeFrame(currentTimeFrame);
        requestPayload.setComparisonTimeFrame(comparisonTimeFrame);
        requestPayload.setFilters(List.of(Filters.builder()
                        .categoryName("traffic_direction")
                        .categoryValue(List.of("INBOUND"))
                        .categoryType("string")
                        .build(),
                Filters.builder()
                        .categoryName("ip")
                        .categoryValue(List.of("********"))
                        .categoryType("string")
                        .build()));
        requestPayload.setSortByFields(Collections.emptyList());
        DerivedMetadata derivedMetadata = new DerivedMetadata(
                "Insights_InfiltrationTraffic_Daily",
                "Insights_InfiltrationTraffic_Daily",
                currentTimeFrame,
                comparisonTimeFrame,
                TableType.AGGREGATED_TABLE,
                TableType.AGGREGATED_TABLE
        );

        RequestContext request = RequestContext.builder()
                .tenantId(Optional.of("tenant_test"))
                .requestPayload(Optional.of(requestPayload))
                .derivedMetadata(Optional.of(derivedMetadata))
                .build();

        String query = repository.getQueryString(request, metadata);

        assertIterableEquals(
                normalizeQueryParts(readFileFromClassPath("unit-test-examples/IpTopDestinationRoles_IpFilter.txt")),
                normalizeQueryParts(query)
        );
    }

    @SneakyThrows
    @Test
    void testGetRiskyTrafficTopSubs_NoFilter() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.RISKY_TRAFFIC_TOP_SUBSCRIPTIONS)
                .tableName("RiskyTraffic")
                .summarizeFields(List.of())
                .groupByFields(List.of())
                .build();

        TimeFrame currentTimeFrame = TimeFrame.builder().startTime("2025-01-02T00:00:00Z").endTime("2025-01-03T00:00:00Z").build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();

        RequestPayload requestPayload = RequestPayload.builder().build();
        requestPayload.setCurrentTimeFrame(currentTimeFrame);
        requestPayload.setComparisonTimeFrame(comparisonTimeFrame);
        requestPayload.setFilters(new ArrayList<>());
        requestPayload.setSortByFields(Collections.emptyList());
        requestPayload.setPagination(Pagination.builder().rowLimit(10).build());
        DerivedMetadata derivedMetadata = new DerivedMetadata(
                "Insights_RiskyTraffic_Daily",
                "Insights_RiskyTraffic_Daily",
                currentTimeFrame,
                comparisonTimeFrame,
                TableType.AGGREGATED_TABLE,
                TableType.AGGREGATED_TABLE
        );

        RequestContext request = RequestContext.builder()
                .tenantId(Optional.of("tenant_test"))
                .requestPayload(Optional.of(requestPayload))
                .derivedMetadata(Optional.of(derivedMetadata))
                .build();

        String query = repository.getQueryString(request, metadata);

        assertIterableEquals(
                normalizeQueryParts(readFileFromClassPath("unit-test-examples/CountryInsights_SubsWithRiskyTraffic_NoFilter.txt")),
                normalizeQueryParts(query)
        );
    }

    @SneakyThrows
    @Test
    void testGetRiskyTrafficTopSubs_PortProtoFilter() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.RISKY_TRAFFIC_TOP_SUBSCRIPTIONS)
                .tableName("RiskyTraffic")
                .summarizeFields(List.of())
                .groupByFields(List.of())
                .build();

        TimeFrame currentTimeFrame = TimeFrame.builder().startTime("2025-01-02T00:00:00Z").endTime("2025-01-03T00:00:00Z").build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();

        RequestPayload requestPayload = RequestPayload.builder().build();
        requestPayload.setCurrentTimeFrame(currentTimeFrame);
        requestPayload.setComparisonTimeFrame(comparisonTimeFrame);
        List<Filters> filters = new ArrayList<>();
        filters.add(Filters.builder()
                .categoryName("port")
                .categoryValue(List.of("8080"))
                .categoryType("number")
                .build());
        filters.add(Filters.builder()
                .categoryName("protocol")
                .categoryValue(List.of("tcp"))
                .categoryType("string")
                .build());
        requestPayload.setFilters(filters);
        requestPayload.setSortByFields(Collections.emptyList());
        requestPayload.setPagination(Pagination.builder().rowLimit(10).build());
        DerivedMetadata derivedMetadata = new DerivedMetadata(
                "Insights_RiskyTraffic_Daily",
                "Insights_RiskyTraffic_Daily",
                currentTimeFrame,
                comparisonTimeFrame,
                TableType.AGGREGATED_TABLE,
                TableType.AGGREGATED_TABLE
        );

        RequestContext request = RequestContext.builder()
                .tenantId(Optional.of("tenant_test"))
                .requestPayload(Optional.of(requestPayload))
                .derivedMetadata(Optional.of(derivedMetadata))
                .build();

        String query = repository.getQueryString(request, metadata);

        assertIterableEquals(
                normalizeQueryParts(readFileFromClassPath("unit-test-examples/CountryInsights_SubsWithRiskyTraffic_ServiceFilter.txt")),
                normalizeQueryParts(query)
        );
    }

    @Test
    void testGetQueryString_GetCspRegions() {
        generateRequestContext();
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.GET_CSP_REGIONS)
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);
        assertTrue(query.contains("project Region = SrcRegion, Zone = SourceZone"), "Query should project source region and zone");
        assertTrue(query.contains("project Region = DestRegion, Zone = DestinationZone"), "Query should project destination region and zone");
        assertTrue(query.contains("distinct Zone, Region"), "Query should select distinct zone and region");
    }


    @Test
    void testGetCrossRegionCustomFilterBaseQuery_WithoutZoneAndRegionFilters() {
        // Setup payload without zone and region filters
        RequestPayload payload = RequestPayload.builder().build();
        List<Filters> filters = Arrays.asList(
            Filters.builder()
                .categoryName("port")
                .categoryValue(Arrays.asList(80, 443))
                .categoryType("number")
                .build(),
            Filters.builder()
                .categoryName("protocol")
                .categoryValue(Arrays.asList("TCP", "UDP"))
                .categoryType("string")
                .build()
        );
        payload.setFilters(filters);

        String tenantId = "testTenant";
        String tableName = "TestTable";
        String timeCondition = "| where StartTime >= datetime('2025-01-01')";
        Fields aggField = AGGREGATED_BYTES;

        try {
            java.lang.reflect.Method method = KustoQueryRepository.class.getDeclaredMethod(
                "getCrossRegionCustomFilterBaseQuery",
                RequestPayload.class, String.class, String.class, String.class, Fields.class
            );
            method.setAccessible(true);
            
            String result = (String) method.invoke(repository, payload, tenantId, tableName, timeCondition, aggField);
            
            // Verify the query contains expected components
            assertTrue(result.contains("TestTable"), "Query should contain table name");
            assertTrue(result.contains("| where IllumioTenantId == 'testTenant'"), "Query should contain tenant filter");
            assertTrue(result.contains("| where StartTime >= datetime('2025-01-01')"), "Query should contain time condition");
            assertTrue(result.contains("| where Port in (80, 443)"), "Query should contain port filter");
            assertTrue(result.contains("| where Proto in ('TCP', 'UDP')"), "Query should contain protocol filter");
            assertTrue(result.contains("| where isnotempty(SourceZone) and isnotempty(DestinationZone)"), "Query should contain zone validation");
            assertTrue(result.contains("| where isnotempty(SrcRegion) and isnotempty(DestRegion)"), "Query should contain region validation");
            assertTrue(result.contains("| where (SourceZone != DestinationZone) or  (SourceZone == DestinationZone and SrcRegion != DestRegion)"), "Query should contain cross-region condition");
            assertTrue(result.contains("| summarize Count=sum(AggByteCount) by SourceZone, SrcRegion, DestinationZone, DestRegion"), "Query should contain summarization");
            
        } catch (Exception e) {
            fail("Failed to invoke private method: " + e.getMessage());
        }
    }

    @Test
    void testGetCrossRegionCustomFilterBaseQuery_WithNullFilters() {
        // Setup payload with null filters
        RequestPayload payload = RequestPayload.builder().build();
        payload.setFilters(null);

        String tenantId = "testTenant";
        String tableName = "TestTable";
        String timeCondition = "| where StartTime >= datetime('2025-01-01')";
        Fields aggField = AGGREGATED_FLOWS;

        try {
            java.lang.reflect.Method method = KustoQueryRepository.class.getDeclaredMethod(
                "getCrossRegionCustomFilterBaseQuery",
                RequestPayload.class, String.class, String.class, String.class, Fields.class
            );
            method.setAccessible(true);
            
            String result = (String) method.invoke(repository, payload, tenantId, tableName, timeCondition, aggField);
            
            // Verify the query contains expected components
            assertTrue(result.contains("TestTable"), "Query should contain table name");
            assertTrue(result.contains("| where IllumioTenantId == 'testTenant'"), "Query should contain tenant filter");
            assertTrue(result.contains("| where StartTime >= datetime('2025-01-01')"), "Query should contain time condition");
            assertTrue(result.contains("| where isnotempty(SourceZone) and isnotempty(DestinationZone)"), "Query should contain zone validation");
            assertTrue(result.contains("| where isnotempty(SrcRegion) and isnotempty(DestRegion)"), "Query should contain region validation");
            assertTrue(result.contains("| where (SourceZone != DestinationZone) or  (SourceZone == DestinationZone and SrcRegion != DestRegion)"), "Query should contain cross-region condition");
            assertTrue(result.contains("| summarize Count=sum(AggFlowCount) by SourceZone, SrcRegion, DestinationZone, DestRegion"), "Query should contain summarization");
            
            // Should not contain zone/region specific filters since they were null
            assertFalse(result.contains("SourceZone == '"), "Query should not contain zone filter when filters are null");
            assertFalse(result.contains("SrcRegion == '"), "Query should not contain region filter when filters are null");
            
        } catch (Exception e) {
            fail("Failed to invoke private method: " + e.getMessage());
        }
    }

    @Test
    void testGetCrossRegionCustomFilterBaseQuery_WithEmptyFilters() {
        // Setup payload with empty filters
        RequestPayload payload = RequestPayload.builder().build();
        payload.setFilters(new ArrayList<>());

        String tenantId = "testTenant";
        String tableName = "TestTable";
        String timeCondition = "| where StartTime >= datetime('2025-01-01')";
        Fields aggField = AGGREGATED_BYTES;

        try {
            java.lang.reflect.Method method = KustoQueryRepository.class.getDeclaredMethod(
                "getCrossRegionCustomFilterBaseQuery",
                RequestPayload.class, String.class, String.class, String.class, Fields.class
            );
            method.setAccessible(true);
            
            String result = (String) method.invoke(repository, payload, tenantId, tableName, timeCondition, aggField);
            
            // Verify the query contains expected components
            assertTrue(result.contains("TestTable"), "Query should contain table name");
            assertTrue(result.contains("| where IllumioTenantId == 'testTenant'"), "Query should contain tenant filter");
            assertTrue(result.contains("| where StartTime >= datetime('2025-01-01')"), "Query should contain time condition");
            assertTrue(result.contains("| where isnotempty(SourceZone) and isnotempty(DestinationZone)"), "Query should contain zone validation");
            assertTrue(result.contains("| where isnotempty(SrcRegion) and isnotempty(DestRegion)"), "Query should contain region validation");
            assertTrue(result.contains("| where (SourceZone != DestinationZone) or  (SourceZone == DestinationZone and SrcRegion != DestRegion)"), "Query should contain cross-region condition");
            assertTrue(result.contains("| summarize Count=sum(AggByteCount) by SourceZone, SrcRegion, DestinationZone, DestRegion"), "Query should contain summarization");
            
        } catch (Exception e) {
            fail("Failed to invoke private method: " + e.getMessage());
        }
    }

    @Test
    void testGetCrossRegionCustomFilterBaseQuery_WithZoneOnlyFilter() {
        // Setup payload with only zone filter (no region)
        RequestPayload payload = RequestPayload.builder().build();
        List<Filters> filters = Arrays.asList(
            Filters.builder()
                .categoryName("zone")
                .categoryValue(Arrays.asList("zone1"))
                .categoryType("string")
                .build(),
            Filters.builder()
                .categoryName("port")
                .categoryValue(Arrays.asList(80))
                .categoryType("number")
                .build()
        );
        payload.setFilters(filters);

        String tenantId = "testTenant";
        String tableName = "TestTable";
        String timeCondition = "| where StartTime >= datetime('2025-01-01')";
        Fields aggField = AGGREGATED_FLOWS;

        try {
            java.lang.reflect.Method method = KustoQueryRepository.class.getDeclaredMethod(
                "getCrossRegionCustomFilterBaseQuery",
                RequestPayload.class, String.class, String.class, String.class, Fields.class
            );
            method.setAccessible(true);
            
            String result = (String) method.invoke(repository, payload, tenantId, tableName, timeCondition, aggField);
            
            // Verify the query contains expected components
            assertTrue(result.contains("TestTable"), "Query should contain table name");
            assertTrue(result.contains("| where IllumioTenantId == 'testTenant'"), "Query should contain tenant filter");
            assertTrue(result.contains("| where StartTime >= datetime('2025-01-01')"), "Query should contain time condition");
            assertTrue(result.contains("| where Port in (80)"), "Query should contain port filter");
            assertTrue(result.contains("| where isnotempty(SourceZone) and isnotempty(DestinationZone)"), "Query should contain zone validation");
            assertTrue(result.contains("| where isnotempty(SrcRegion) and isnotempty(DestRegion)"), "Query should contain region validation");
            assertTrue(result.contains("| where (SourceZone != DestinationZone) or  (SourceZone == DestinationZone and SrcRegion != DestRegion)"), "Query should contain cross-region condition");
            assertTrue(result.contains("| summarize Count=sum(AggFlowCount) by SourceZone, SrcRegion, DestinationZone, DestRegion"), "Query should contain summarization");
            
            // Should not contain zone/region specific filters since only zone was provided
            assertFalse(result.contains("SourceZone == 'zone1' and SrcRegion == '"), "Query should not contain zone and region filter when only zone is provided");
            assertFalse(result.contains("DestinationZone == 'zone1' and DestRegion == '"), "Query should not contain destination zone and region filter when only zone is provided");
            
        } catch (Exception e) {
            fail("Failed to invoke private method: " + e.getMessage());
        }
    }

    @Test
    void testGetCrossRegionCustomFilterBaseQuery_WithRegionOnlyFilter() {
        // Setup payload with only region filter (no zone)
        RequestPayload payload = RequestPayload.builder().build();
        List<Filters> filters = Arrays.asList(
            Filters.builder()
                .categoryName("region")
                .categoryValue(Arrays.asList("region1"))
                .categoryType("string")
                .build(),
            Filters.builder()
                .categoryName("protocol")
                .categoryValue(Arrays.asList("TCP"))
                .categoryType("string")
                .build()
        );
        payload.setFilters(filters);

        String tenantId = "testTenant";
        String tableName = "TestTable";
        String timeCondition = "| where StartTime >= datetime('2025-01-01')";
        Fields aggField = AGGREGATED_BYTES;

        try {
            java.lang.reflect.Method method = KustoQueryRepository.class.getDeclaredMethod(
                "getCrossRegionCustomFilterBaseQuery",
                RequestPayload.class, String.class, String.class, String.class, Fields.class
            );
            method.setAccessible(true);
            
            String result = (String) method.invoke(repository, payload, tenantId, tableName, timeCondition, aggField);
            
            // Verify the query contains expected components
            assertTrue(result.contains("TestTable"), "Query should contain table name");
            assertTrue(result.contains("| where IllumioTenantId == 'testTenant'"), "Query should contain tenant filter");
            assertTrue(result.contains("| where StartTime >= datetime('2025-01-01')"), "Query should contain time condition");
            assertTrue(result.contains("| where Proto in ('TCP')"), "Query should contain protocol filter");
            assertTrue(result.contains("| where isnotempty(SourceZone) and isnotempty(DestinationZone)"), "Query should contain zone validation");
            assertTrue(result.contains("| where isnotempty(SrcRegion) and isnotempty(DestRegion)"), "Query should contain region validation");
            assertTrue(result.contains("| where (SourceZone != DestinationZone) or  (SourceZone == DestinationZone and SrcRegion != DestRegion)"), "Query should contain cross-region condition");
            assertTrue(result.contains("| summarize Count=sum(AggByteCount) by SourceZone, SrcRegion, DestinationZone, DestRegion"), "Query should contain summarization");
            
            // Should not contain zone/region specific filters since only region was provided
            assertFalse(result.contains("SourceZone == '"), "Query should not contain zone filter when only region is provided");
            assertFalse(result.contains("SrcRegion == 'region1'"), "Query should not contain region filter when only region is provided");
            
        } catch (Exception e) {
            fail("Failed to invoke private method: " + e.getMessage());
        }
    }

    @Test
    void testGetCrossRegionCustomFilterBaseQuery_OriginalFiltersNotModified() {
        // Setup payload with zone and region filters that should be removed during processing
        RequestPayload payload = RequestPayload.builder().build();
        List<Filters> originalFilters = Arrays.asList(
            Filters.builder()
                .categoryName("zone")
                .categoryValue(Arrays.asList("zone1"))
                .categoryType("string")
                .build(),
            Filters.builder()
                .categoryName("region")
                .categoryValue(Arrays.asList("region1"))
                .categoryType("string")
                .build(),
            Filters.builder()
                .categoryName("port")
                .categoryValue(Arrays.asList(80, 443))
                .categoryType("number")
                .build()
        );
        payload.setFilters(originalFilters);

        String tenantId = "testTenant";
        String tableName = "TestTable";
        String timeCondition = "| where StartTime >= datetime('2025-01-01')";
        Fields aggField = AGGREGATED_BYTES;

        try {
            java.lang.reflect.Method method = KustoQueryRepository.class.getDeclaredMethod(
                "getCrossRegionCustomFilterBaseQuery",
                RequestPayload.class, String.class, String.class, String.class, Fields.class
            );
            method.setAccessible(true);
            
            String result = (String) method.invoke(repository, payload, tenantId, tableName, timeCondition, aggField);
            
            // Verify the query contains expected components
            assertTrue(result.contains("TestTable"), "Query should contain table name");
            assertTrue(result.contains("| where IllumioTenantId == 'testTenant'"), "Query should contain tenant filter");
            assertTrue(result.contains("| where StartTime >= datetime('2025-01-01')"), "Query should contain time condition");
            assertTrue(result.contains("| where Port in (80, 443)"), "Query should contain port filter");
            assertTrue(result.contains("| where SourceZone in~ ('zone1') and SrcRegion in~ ('region1')"), "Query should contain zone and region filter");
            assertTrue(result.contains("| where isnotempty(SourceZone) and isnotempty(DestinationZone)"), "Query should contain zone validation");
            assertTrue(result.contains("| where isnotempty(SrcRegion) and isnotempty(DestRegion)"), "Query should contain region validation");
            assertTrue(result.contains("| where (SourceZone != DestinationZone) or  (SourceZone == DestinationZone and SrcRegion != DestRegion)"), "Query should contain cross-region condition");
            assertTrue(result.contains("| summarize Count=sum(AggByteCount) by SourceZone, SrcRegion, DestinationZone, DestRegion"), "Query should contain summarization");
            
            // CRITICAL: Verify that the original filters list was not modified
            // The method should create a copy and modify the copy, not the original
            assertIterableEquals(originalFilters, payload.getFilters(), "Original filters list should remain unchanged");
            
            // Verify that zone and region filters are still present in the original list
            boolean hasZoneFilter = payload.getFilters().stream()
                .anyMatch(f -> "zone".equalsIgnoreCase(f.getCategoryName()));
            boolean hasRegionFilter = payload.getFilters().stream()
                .anyMatch(f -> "region".equalsIgnoreCase(f.getCategoryName()));
            
            assertTrue(hasZoneFilter, "Original filters should still contain zone filter");
            assertTrue(hasRegionFilter, "Original filters should still contain region filter");
            
        } catch (Exception e) {
            fail("Failed to invoke private method: " + e.getMessage());
        }
    }

    private void generateRequestContext() {
        TimeFrame currentTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();

        Pagination pagination = Pagination.builder().build();
        pagination.setPageNumber(1);
        pagination.setRowLimit(10);

        payload = RequestPayload.builder().build();
        payload.setCurrentTimeFrame(currentTimeFrame);
        payload.setComparisonTimeFrame(comparisonTimeFrame);
        payload.setPagination(pagination);
        payload.setFilters(List.of(Filters.builder()
                        .categoryName("traffic_direction")
                        .categoryValue(List.of("INBOUND"))
                        .categoryType("string")
                        .build(),
                Filters.builder()
                        .categoryName("category")
                        .categoryValue(List.of("subscriptions"))
                        .categoryType("string")
                        .build()));
        payload.setSortByFields(Collections.emptyList());
        DerivedMetadata derivedMetadata = new DerivedMetadata(
                "Insights_RiskyTraffic_Hourly",
                "Insights_RiskyTraffic_Hourly",
                currentTimeFrame,
                comparisonTimeFrame,
                TableType.AGGREGATED_TABLE,
                TableType.AGGREGATED_TABLE
        );

        requestContext = RequestContext.builder()
                .tenantId(Optional.of(tenantId))
                .requestPayload(Optional.of(payload))
                .derivedMetadata(Optional.of(derivedMetadata))
                .build();
    }

    // This is a utility method to split the query by KQL delimiters
    // It is helpful to compare unformatted query generated by code with
    // the formatted KQL query defined in unit test examples
    private List<String> normalizeQueryParts(String query) {
        // Split by pipe and semicolon,
        // Trim the whitespaces,
        // Remove the intermediate newline char, and skip empty lines
        return Arrays.stream(query.split("[|;]"))
                .map(String::trim)
                .map(s -> s.replaceAll("\\s*[\r\n]+\\s*", " "))
                .filter(s -> !s.isEmpty())
                .toList();
    }

    // Read the class path file content as String
    private String readFileFromClassPath(String filePath) throws IOException {
        Resource resource = new ClassPathResource(filePath);
        return new String(Files.readAllBytes(resource.getFile().toPath()));
    }
}
