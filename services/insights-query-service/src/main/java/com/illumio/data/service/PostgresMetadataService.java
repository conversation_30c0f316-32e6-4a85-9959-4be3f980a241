package com.illumio.data.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.model.RequestMetadataTableEntity;
import com.illumio.data.model.constants.Fields;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.constants.Template;
import com.illumio.data.repository.RequestMetadataRepository;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;

@Service
public class PostgresMetadataService implements MetadataService{
    private final RequestMetadataRepository repository;
    private final ObjectMapper objectMapper = new ObjectMapper();

    public PostgresMetadataService(RequestMetadataRepository repository) {
        this.repository = repository;
    }

    @SuppressWarnings("unchecked")
    public Mono<Metadata> getMetadata(String widgetId) {
        return repository.findByWidgetId(widgetId)
                .switchIfEmpty(Mono.empty())
                .flatMap(entity -> {
                    try {
                        return Mono.fromCallable(() -> mapToMetadata(entity));
                    } catch (Exception e) {
                        return Mono.error(new RuntimeException("Failed to map metadata for widgetId: " + widgetId, e));
                    }
                });
    }
    
    public Flux<Metadata> getWidgetsByPageId(String pageId) {
        return repository.findByPageId(pageId)
                .flatMap(entity -> {
                    try {
                        return Mono.fromCallable(() -> mapToMetadata(entity));
                    } catch (Exception e) {
                        return Mono.error(new RuntimeException("Failed to map metadata for pageId: " + pageId + ", widgetId: " + entity.getWidgetId(), e));
                    }
                });
    }

    private Metadata mapToMetadata(RequestMetadataTableEntity entity) {
        try {
            JsonNode jsonNode = objectMapper.readTree(entity.getMetadata()); // JSONB string

            return Metadata.builder()
                    .widgetId(entity.getWidgetId())
                    .requestType(entity.getRequestType())
                    .pageId(entity.getPageId())
                    .template(Template.valueOf(jsonNode.get("templateType").asText()))
                    .tableName(jsonNode.get("tableName").asText())
                    .summarizeFields(parseFields(jsonNode.get("summarizeFields")))
                    .groupByFields(parseFields(jsonNode.get("groupByFields")))
                    .isDirectionDependent(jsonNode.get("isDirectionDependent").asBoolean())
                    .isCategoryDependent(jsonNode.get("isCategoryDependent").asBoolean())
                    .build();

        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to parse metadata JSON", e);
        }
    }

    private List<Fields> parseFields(JsonNode jsonArrayNode) {
        List<Fields> fieldsList = new ArrayList<>();
        if (jsonArrayNode != null && jsonArrayNode.isArray()) {
            for (JsonNode node : jsonArrayNode) {
                Fields field = Fields.getFieldByFieldKey(node.asText());
                fieldsList.add(field);
            }
        }
        return fieldsList;
    }
}
